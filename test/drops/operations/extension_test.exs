defmodule Drops.Operations.ExtensionTest do
  use Drops.OperationCase, async: false

  Code.require_file("test/support/test_extension.ex")
  Code.require_file("test/support/manual_extension.ex")

  alias Test.Extensions, as: Exts

  describe "extension registration" do
    test "register_extension/1 adds new extensions to registry" do
      defmodule Test.MyOperations do
        use Drops.Operations

        register_extension(Exts.PrepareExtension)
        register_extension(Exts.ManualExtension)
      end

      assert Test.MyOperations.registered_extensions() == [
               Exts.PrepareExtension,
               Exts.ManualExtension
             ]
    end

    test "operations inherit extensions from base module" do
      defmodule Test.MyOperationsWithExtensions do
        use Drops.Operations

        register_extension(Exts.PrepareExtension)
      end

      defmodule Test.MyOperation do
        use Test.MyOperationsWithExtensions

        def execute(context) do
          {:ok, context}
        end
      end

      assert Test.MyOperationsWithExtensions.registered_extensions() == [
               Exts.PrepareExtension
             ]
    end
  end

  describe "extension behavior verification" do
    test "PrepareExtension modifies params in prepare step" do
      defmodule Test.PrepareOperations do
        use Drops.Operations

        register_extension(Exts.PrepareExtension)
      end

      defmodule Test.PrepareOperation do
        use Test.PrepareOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      {:ok, result} = Test.PrepareOperation.call(%{params: %{name: "test"}})
      assert result == %{name: "prepared_test", prepared: true}
    end

    test "ValidateExtension adds custom validation" do
      defmodule Test.ValidateOperations do
        use Drops.Operations

        register_extension(Exts.ValidateExtension)
      end

      defmodule Test.ValidateOperation do
        use Test.ValidateOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      {:ok, result} = Test.ValidateOperation.call(%{params: %{name: "valid_name"}})
      assert result == %{name: "valid_name"}

      {:error, error} = Test.ValidateOperation.call(%{params: %{name: "invalid_name"}})
      assert error == "name cannot contain 'invalid'"
    end

    test "multiple extensions work together" do
      defmodule Test.MultiExtensionOperations do
        use Drops.Operations

        register_extension(Exts.PrepareExtension)
        register_extension(Exts.ValidateExtension)
      end

      defmodule Test.MultiExtensionOperation do
        use Test.MultiExtensionOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      {:ok, result} = Test.MultiExtensionOperation.call(%{params: %{name: "test"}})
      assert result == %{name: "prepared_test", prepared: true}

      {:error, error} = Test.MultiExtensionOperation.call(%{params: %{name: "invalid"}})
      assert error == "name cannot contain 'invalid'"
    end

    test "CallbackExtension uses new simplified UnitOfWork API" do
      # Clean up process dictionary before test
      Process.delete(:before_prepare_called)

      defmodule Test.CallbackOperations do
        use Drops.Operations

        register_extension(Exts.CallbackExtension)
      end

      defmodule Test.CallbackOperation do
        use Test.CallbackOperations

        schema do
          %{
            required(:name) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      # Check that callbacks are registered in the UnitOfWork
      uow = Test.CallbackOperation.__unit_of_work__()

      # Verify before callback is registered
      before_callbacks = Map.get(uow.callbacks.before, :prepare, [])
      assert length(before_callbacks) == 1
      assert {Test.CallbackOperation, :log_before_prepare, nil} in before_callbacks

      # Verify after callback is registered
      after_callbacks = Map.get(uow.callbacks.after, :prepare, [])
      assert length(after_callbacks) == 1
      assert {Test.CallbackOperation, :log_after_prepare, nil} in after_callbacks

      # Clean up process dictionary after test
      Process.delete(:before_prepare_called)
    end

    test "CallbackExtension demonstrates before_step and after_step API" do
      defmodule Test.CallbackTestOperations do
        use Drops.Operations

        register_extension(Exts.CallbackExtension)
      end

      defmodule Test.CallbackTestOperation do
        use Test.CallbackTestOperations

        schema do
          %{
            required(:data) => string()
          }
        end

        @impl true
        def execute(%{params: params}) do
          {:ok, params}
        end
      end

      # Verify that the new simplified API functions were used to register callbacks
      uow = Test.CallbackTestOperation.__unit_of_work__()

      # Check that before_step was used to register the before callback
      before_callbacks = Map.get(uow.callbacks.before, :prepare, [])
      assert length(before_callbacks) == 1
      assert {Test.CallbackTestOperation, :log_before_prepare, nil} in before_callbacks

      # Check that after_step was used to register the after callback
      after_callbacks = Map.get(uow.callbacks.after, :prepare, [])
      assert length(after_callbacks) == 1
      assert {Test.CallbackTestOperation, :log_after_prepare, nil} in after_callbacks
    end
  end
end
